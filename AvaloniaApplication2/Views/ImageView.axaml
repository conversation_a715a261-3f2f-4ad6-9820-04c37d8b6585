<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:vm="using:AvaloniaApplication2.ViewModels"
             xmlns:material="using:IconPacks.Avalonia.Material"
             xmlns:iconPacks="https://github.com/MahApps/IconPacks.Avalonia"
             xmlns:conv="using:AvaloniaApplication2.Converters"
             xmlns:controls="clr-namespace:Avalonia.Controls;assembly=Avalonia.Controls"
             xmlns:iviewer="using:ImageViewer.Controls"
             mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="720"
             x:Class="AvaloniaApplication2.Views.ImageView"
             x:DataType="vm:ImageViewModel"
             x:Name="ImageRoot"
             Focusable="True"
             Loaded="ImageView_Loaded">

    <UserControl.Resources>
        <conv:PathToBitmapConverter x:Key="PathToBitmapConverter"/>
        <conv:MultiplyConverter x:Key="MultiplyConverter"/>
    </UserControl.Resources>

	<Grid ColumnDefinitions="250,*,250">

		<!-- Left Panel: Tag list + create (match Gallery style) -->
		<Border Grid.Column="0" Background="#2E2E2E" Padding="15">
			<ScrollViewer>
				<StackPanel Spacing="15">

					<Grid ColumnDefinitions="*,Auto" VerticalAlignment="Center">
						<TextBlock Text="标签类别" Foreground="White" FontWeight="Bold"/>
						<Button Grid.Column="1" Background="#3C3C3C" BorderBrush="#4A4A4A" CornerRadius="4" Padding="6,2" x:Name="BtnAddTag" Click="OnTagAddButtonClick">
							<iconPacks:PackIconMaterial Kind="Plus" Foreground="White"/>
						</Button>
					</Grid>

					<TextBlock Text="为图像设置类别:" Foreground="LightGray" Margin="0,6,0,4"/>

					<ItemsControl ItemsSource="{Binding Tags}">
						<ItemsControl.ItemTemplate>
							<DataTemplate>
								<Button x:Name="TagItemButton"
										Command="{Binding DataContext.AssignTagToCurrentCommand, ElementName=ImageRoot}"
										CommandParameter="{Binding}"
										Background="#3C3C3C" BorderBrush="#4A4A4A" CornerRadius="6" Margin="0,4,0,0" Padding="8,6" HorizontalAlignment="Stretch">
									<Grid ColumnDefinitions="Auto,Auto,*,Auto">
										<!-- Icon with color -->
										<iconPacks:PackIconMaterial Grid.Column="0" Kind="{Binding IconKind}" Foreground="{Binding Color}" Margin="0,0,8,0" VerticalAlignment="Center"/>
										<!-- Colored id badge -->
										<Border Grid.Column="1" Background="{Binding Color}" CornerRadius="4" Padding="6,0" VerticalAlignment="Center">
											<TextBlock Text="{Binding Id}" Foreground="White" FontWeight="Bold"/>
										</Border>
										<!-- Name -->
										<TextBlock Grid.Column="2" Text="{Binding Name}" Foreground="White" VerticalAlignment="Center" Margin="8,0,0,0"/>
										<!-- Hover delete button -->
										<Button Grid.Column="3"
												Background="#3C3C3C" BorderBrush="#4A4A4A" CornerRadius="4" Padding="2" Margin="6,0,0,0"
												IsVisible="{Binding IsPointerOver, ElementName=TagItemButton}"
												Command="{Binding DataContext.DeleteTagCommand, ElementName=ImageRoot}"
												CommandParameter="{Binding}">
											<iconPacks:PackIconMaterial Kind="TrashCanOutline" Foreground="White" Width="16" Height="16"/>
										</Button>
									</Grid>
								</Button>
							</DataTemplate>
						</ItemsControl.ItemTemplate>
					</ItemsControl>

					<!-- Add Tag Popup (same as Gallery) -->
                    <Popup x:Name="PopupAddTag" Placement="Right">
                        <Border Background="#2E2E2E" Padding="10" CornerRadius="6" BorderBrush="#4A4A4A" BorderThickness="1">
                            <StackPanel Spacing="8" MinWidth="260">
                                <TextBlock Text="新建标签" Foreground="White" FontWeight="Bold"/>
                                <StackPanel>
                                    <TextBlock Text="名称" Foreground="LightGray"/>
                                    <TextBox Text="{Binding NewTagName, Mode=TwoWay}" KeyDown="OnNewTagNameKeyDown"/>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Text="颜色" Foreground="LightGray"/>
                                    <ComboBox ItemsSource="{Binding PresetColors}" SelectedItem="{Binding NewTagColor, Mode=TwoWay}">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Spacing="8">
                                                    <Border Width="16" Height="16" CornerRadius="3" Background="{Binding}"/>
                                                    <TextBlock Text="{Binding}" Foreground="White"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Text="状态" Foreground="LightGray"/>
                                    <StackPanel Orientation="Horizontal" Spacing="8">
                                        <RadioButton GroupName="NewTagStatusGroup" IsChecked="{Binding NewTagIsOk, Mode=TwoWay}" Padding="10,6" MinWidth="80">
                                            <StackPanel Orientation="Horizontal" Spacing="6">
                                                <iconPacks:PackIconMaterial Kind="ThumbUp"/>
                                                <TextBlock Text="良好"/>
                                            </StackPanel>
                                        </RadioButton>
                                        <RadioButton GroupName="NewTagStatusGroup" IsChecked="{Binding NewTagIsOk, Converter={x:Static conv:BooleanNegationConverter.Instance}, Mode=TwoWay}" Padding="10,6" MinWidth="80">
                                            <StackPanel Orientation="Horizontal" Spacing="6">
                                                <iconPacks:PackIconMaterial Kind="ThumbDown"/>
                                                <TextBlock Text="异常"/>
                                            </StackPanel>
                                        </RadioButton>
                                    </StackPanel>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="8" HorizontalAlignment="Right">
                                    <Button Content="取消" Click="OnTagAddCancelClick"/>
                                    <Button Content="创建" Command="{Binding AddTagCommand}" Click="OnTagCreateClick"/>
                                </StackPanel>
                            </StackPanel>
                        </Border>
                    </Popup>

					<Separator Margin="0,10"/>

				</StackPanel>
			</ScrollViewer>
		</Border>

		<!-- Center Panel -->
		<DockPanel Grid.Column="1">

			<Border DockPanel.Dock="Top" Padding="5" Background="{DynamicResource SystemControlPageBackgroundAltHighBrush}">
				<StackPanel Orientation="Horizontal" Spacing="8" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Grid Width="32" Height="32">
                        <ToggleButton x:Name="ToolPencilButton" Classes="Flat" Click="OnToolPencilClick" ToolTip.Tip="画笔">
                            <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.Pencil}" />
                        </ToggleButton>
                        <Button Width="14" Height="14" Classes="Flat" VerticalAlignment="Bottom" HorizontalAlignment="Right" Margin="0,0,0,0">
                            <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.ChevronDown}" Width="12" Height="12" />
                            <Button.Flyout>
                                <Flyout Placement="Bottom" Opened="OnPencilFlyoutOpened">
                                    <StackPanel Margin="8" Spacing="8">
                                        <TextBlock Text="粗细"/>
                                        <Slider x:Name="PencilSizeSlider" Width="160" Minimum="0.2" Maximum="10" SmallChange="0.1" LargeChange="0.5" Value="5.1" ValueChanged="OnPencilSizeChanged"/>
                                        <TextBlock Text="颜色" Margin="0,4,0,0"/>
                                        <UniformGrid x:Name="PencilColorsGrid" Columns="6" Rows="2" Margin="0,0,0,4">
                                            <Button Width="18" Height="18" Background="Red" Click="OnPencilColorPick" Tag="Red"/>
                                            <Button Width="18" Height="18" Background="Orange" Click="OnPencilColorPick" Tag="Orange"/>
                                            <Button Width="18" Height="18" Background="DeepPink" Click="OnPencilColorPick" Tag="DeepPink"/>
                                            <Button Width="18" Height="18" Background="Yellow" Click="OnPencilColorPick" Tag="Yellow"/>
                                            <Button Width="18" Height="18" Background="Lime" Click="OnPencilColorPick" Tag="Lime"/>
                                            <Button Width="18" Height="18" Background="Cyan" Click="OnPencilColorPick" Tag="Cyan"/>
                                        </UniformGrid>
                                        <StackPanel Orientation="Horizontal" Spacing="6" VerticalAlignment="Center">
                                            <TextBlock Text="当前:"/>
                                            <Border x:Name="PencilColorPreview" Width="24" Height="14" CornerRadius="2" BorderBrush="Gray" BorderThickness="1"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Flyout>
                            </Button.Flyout>
                        </Button>
                    </Grid>
                    <Grid Width="32" Height="32">
                        <ToggleButton x:Name="ToolEraserButton" Classes="Flat" Click="OnToolEraserClick" ToolTip.Tip="橡皮擦">
                            <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.EraserVariant}" />
                        </ToggleButton>
                        <Button Width="14" Height="14" Classes="Flat" VerticalAlignment="Bottom" HorizontalAlignment="Right" Margin="0,0,0,0">
                            <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.ChevronDown}" Width="12" Height="12" />
                            <Button.Flyout>
                                <Flyout Placement="Bottom" Opened="OnEraserFlyoutOpened">
                                    <StackPanel Margin="8" Spacing="8">
                                        <TextBlock Text="橡皮大小"/>
                                        <Slider x:Name="EraserSizeSlider" Width="160" Minimum="1" Maximum="40" SmallChange="1" LargeChange="2" Value="20.5" ValueChanged="OnEraserSizeChanged"/>
                                    </StackPanel>
                                </Flyout>
                            </Button.Flyout>
                        </Button>
                    </Grid>
                    <ToggleButton x:Name="ToolPolygonButton" Width="32" Height="32" Classes="Flat" Click="OnToolPolygonClick" ToolTip.Tip="多边形">
                        <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.VectorPolygon}" />
                    </ToggleButton>
                    <ToggleButton x:Name="ToolRectangleButton" Width="32" Height="32" Classes="Flat" Click="OnToolRectangleClick" ToolTip.Tip="矩形">
                        <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.VectorRectangle}" />
                    </ToggleButton>
                    <ToggleButton x:Name="ToolCirleButton" Width="32" Height="32" Classes="Flat" Click="OnToolCircleClick" ToolTip.Tip="圆形">
                        <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.VectorCircle}" />
                    </ToggleButton>
                    <ToggleButton x:Name="ToolLineButton" Width="32" Height="32" Classes="Flat" Click="OnToolLineClick" ToolTip.Tip="直线">
                        <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.VectorLine}" />
                    </ToggleButton>
                    <ToggleButton x:Name="ToolCleanButton" Width="32" Height="32" Classes="Flat" Click="OnToolCleanClick" ToolTip.Tip="清空">
                        <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.AutoFix}" />
                    </ToggleButton>
                    <Separator/>
                    <Button Click="OnApplyEditsClick" Classes="Accent" Height="32" ToolTip.Tip="应用到图像并同步图库">
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.ContentSaveOutline}" />
                            <TextBlock Text="应用"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Border>

			<Border Background="{DynamicResource SystemControlPageBackgroundBaseLowBrush}" Margin="10" CornerRadius="4">
				<Grid x:Name="LegacyCenterGrid">
					<!-- RenderView as the single rendering layer (image + overlays) with built-in zoom/pan -->
					<iviewer:RenderView x:Name="RenderView"
										IsVisible="True"
										HorizontalAlignment="Stretch"
										VerticalAlignment="Stretch"
										Source="{Binding DisplayImagePath, Converter={StaticResource PathToBitmapConverter}}"/>

					<!-- Overlay navigation arrows -->
					<Grid>
						<Grid.ColumnDefinitions>
							<ColumnDefinition Width="*"/>
							<ColumnDefinition Width="Auto"/>
							<ColumnDefinition Width="*"/>
						</Grid.ColumnDefinitions>
						<!-- Left arrow -->
						<RepeatButton x:Name="PrevButton" Grid.Column="0" HorizontalAlignment="Left" VerticalAlignment="Center"
                                      Margin="12" Padding="10" CornerRadius="8"
                                      Opacity="0.7" Background="#66000000" Cursor="Hand"
                                      ToolTip.Tip="上一张" Delay="180" Interval="120" Click="OnPrevImageClick">
							<material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.ChevronLeft}" Width="26" Height="26"/>
						</RepeatButton>
						<!-- Right arrow -->
						<RepeatButton x:Name="NextButton" Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center"
                                      Margin="12" Padding="10" CornerRadius="8"
                                      Opacity="0.7" Background="#66000000" Cursor="Hand"
                                      ToolTip.Tip="下一张" Delay="180" Interval="120" Click="OnNextImageClick">
							<material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.ChevronRight}" Width="26" Height="26"/>
						</RepeatButton>
						<!-- Center counter -->
						<Border Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Bottom" Margin="0,0,0,16"
								Background="#55000000" CornerRadius="8" Padding="10,4" IsHitTestVisible="False">
							<TextBlock Text="{Binding GalleryRef.ImageCountText}" Foreground="White"/>
						</Border>
					</Grid>

					<!-- Styles for hover/pressed -->
					<Grid.Styles>
						<Style Selector="RepeatButton#PrevButton, RepeatButton#NextButton">
							<Setter Property="Transitions">
								<Transitions>
									<DoubleTransition Property="Opacity" Duration="0:0:0.12" Easing="QuadraticEaseOut"/>
								</Transitions>
							</Setter>
							<Setter Property="RenderTransform">
								<ScaleTransform ScaleX="1" ScaleY="1"/>
							</Setter>
						</Style>
						<Style Selector="RepeatButton#PrevButton:pointerover, RepeatButton#NextButton:pointerover">
							<Setter Property="Opacity" Value="1"/>
						</Style>
						<Style Selector="RepeatButton#PrevButton:pressed, RepeatButton#NextButton:pressed">
							<Setter Property="RenderTransform">
								<ScaleTransform ScaleX="0.94" ScaleY="0.94"/>
							</Setter>
						</Style>
						<Style Selector="RepeatButton#PrevButton:disabled, RepeatButton#NextButton:disabled">
							<Setter Property="Opacity" Value="0.35"/>
						</Style>
					</Grid.Styles>

					<!-- Top-right tag badge (display-only) -->
					<Border
						IsVisible="{Binding CurrentTagId, Converter={x:Static conv:IntGreaterThanZeroToBoolConverter.Instance}}"
						HorizontalAlignment="Right" VerticalAlignment="Top" Margin="8"
						CornerRadius="8" Padding="6,4">
						<Border.Background>
							<MultiBinding Converter="{x:Static conv:TagIdToColorConverter.Instance}">
								<Binding Path="CurrentTagId"/>
								<Binding Path="DataContext.Tags" ElementName="ImageRoot"/>
							</MultiBinding>
						</Border.Background>
						<iconPacks:PackIconMaterial Foreground="White" Width="18" Height="18">
							<iconPacks:PackIconMaterial.Kind>
								<MultiBinding Converter="{x:Static conv:TagIdToIconKindConverter.Instance}">
									<Binding Path="CurrentTagId"/>
									<Binding Path="DataContext.Tags" ElementName="ImageRoot"/>
								</MultiBinding>
							</iconPacks:PackIconMaterial.Kind>
						</iconPacks:PackIconMaterial>
					</Border>
					<TextBlock Text="Image will be displayed here" IsVisible="False" HorizontalAlignment="Center" VerticalAlignment="Center"/>

					<!-- Bottom-left status (mouse or selected ROI info) -->
					<Border HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="10" Padding="6,3" CornerRadius="6"
							Background="#66000000" IsHitTestVisible="False">
						<TextBlock x:Name="StatusText" Foreground="White" FontSize="11"/>
					</Border>
				</Grid>
			</Border>

		</DockPanel>

		<!-- Right Panel -->
		<Border Grid.Column="2" Background="{DynamicResource SystemControlPageBackgroundAltHighBrush}" Padding="5">
			<DockPanel LastChildFill="True">
				<!-- Bottom Progress Bar -->
				<Border DockPanel.Dock="Bottom" Padding="5" Background="{DynamicResource SystemControlPageBackgroundAltHighBrush}">
					<Grid ColumnDefinitions="Auto,*,Auto">
						<TextBlock Grid.Column="0" Text="已标注的图像:" VerticalAlignment="Center" Margin="0,0,10,0"/>
						<Border Grid.Column="1" Background="#00A099" CornerRadius="3" Padding="10,2">
							<TextBlock Text="{Binding GalleryRef.AnnotationProgressPercent, StringFormat={}{0}%}" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
						</Border>
						<TextBlock Grid.Column="2" Text="{Binding GalleryRef.AnnotationProgressText}" VerticalAlignment="Center" FontWeight="Bold" Margin="10,0,0,0"/>
					</Grid>
				</Border>

				<!-- Scrollable Main Content -->
				<ScrollViewer>
						<StackPanel Spacing="5" Margin="5">
							<!-- Navigator -->
							<TextBlock Text="导航器" FontWeight="Bold"/>
							<Grid Width="200" Height="120" Background="#202020" HorizontalAlignment="Left" x:Name="NavigatorHost">
                                <Image Source="{Binding DisplayImagePath, Converter={StaticResource PathToBitmapConverter}}" Stretch="UniformToFill"/>
                                <!-- Viewport rectangle -->
                                <Canvas x:Name="NavigatorCanvas" PointerPressed="OnNavigatorPointerPressed" PointerMoved="OnNavigatorPointerMoved" PointerReleased="OnNavigatorPointerReleased" PointerWheelChanged="OnNavigatorWheelChanged">
                                    <Border x:Name="NavigatorRect" BorderBrush="#00E0B8" BorderThickness="1" Background="#2000E0B8"/>
                                </Canvas>
                            </Grid>

							<Grid ColumnDefinitions="Auto,*,Auto,Auto,Auto" Margin="0,4,0,0">
                                <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.MagnifyMinus}" VerticalAlignment="Center"/>
                                <Slider Grid.Column="1" Minimum="0.25" Maximum="20.0" SmallChange="0.05" LargeChange="0.1" Value="{Binding PreviewZoom, Mode=TwoWay}" Margin="5,0"
                                        TickPlacement="BottomRight" TickFrequency="1"/>
                                <material:PackIconMaterial Grid.Column="2" Kind="{x:Static material:PackIconMaterialKind.MagnifyPlus}" VerticalAlignment="Center"/>
                                <TextBlock Grid.Column="3" Text="{Binding ZoomPercent, StringFormat={}{0}%}" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                <Button Grid.Column="4" Classes="Flat" ToolTip.Tip="适应窗口（完整可见）" Click="OnFitToViewportClick" Margin="6,0,0,0">
                                    <material:PackIconMaterial Kind="{x:Static material:PackIconMaterialKind.FitToPageOutline}" />
                                </Button>
                            </Grid>

						<!-- Sliders -->
						<Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto" Margin="0,5,0,0">
							<TextBlock Grid.Row="0" Grid.Column="0" Text="不透明度" VerticalAlignment="Center"/>
							<Slider Grid.Row="0" Grid.Column="1" Minimum="0" Maximum="1" SmallChange="0.01" LargeChange="0.1" Value="{Binding OpacityFactor, Mode=TwoWay}" Margin="10,0,0,0"/>
							<TextBlock Grid.Row="1" Grid.Column="0" Text="亮度" VerticalAlignment="Center"/>
							<Slider Grid.Row="1" Grid.Column="1" Minimum="0.5" Maximum="1.5" SmallChange="0.01" LargeChange="0.1" Value="{Binding BrightnessFactor, Mode=TwoWay}" Margin="10,0,0,0"/>
							<TextBlock Grid.Row="2" Grid.Column="0" Text="对比度" VerticalAlignment="Center"/>
							<Slider Grid.Row="2" Grid.Column="1" Minimum="0.5" Maximum="1.5" SmallChange="0.01" LargeChange="0.1" Value="{Binding ContrastFactor, Mode=TwoWay}" Margin="10,0,0,0"/>
						</Grid>

						<Separator Margin="0,10"/>

						<!-- Instance List -->
						<TextBlock Text="标签实例" FontWeight="Bold"/>
						<Grid ColumnDefinitions="*,Auto,Auto" Margin="0,5,0,5">
							<TextBlock Grid.Column="0" Text="类别" Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
							<TextBlock Grid.Column="1" Text="类型" Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
							<TextBlock Grid.Column="2" Text="面积 [像素]" Foreground="{DynamicResource SystemControlForegroundBaseMediumBrush}"/>
						</Grid>
						<ListBox ItemsSource="{Binding InstanceItems}" Background="Transparent">
							<ListBox.ItemTemplate>
								<DataTemplate>
									<Grid ColumnDefinitions="Auto,*,Auto,Auto">
										<Border Grid.Column="0" Width="12" Height="12" Background="{Binding Color}" CornerRadius="2" Margin="0,0,10,0"/>
										<TextBlock Grid.Column="1" Text="{Binding Name}"/>
										<TextBlock Grid.Column="2" Text="{Binding Type}" Margin="10,0"/>
										<TextBlock Grid.Column="3" Text="{Binding Area}" HorizontalAlignment="Right"/>
									</Grid>
								</DataTemplate>
							</ListBox.ItemTemplate>
						</ListBox>

						<Separator Margin="0,10"/>

						<!-- Comment Box -->
						<TextBox Watermark="撰写评论..." Height="80" TextWrapping="Wrap" AcceptsReturn="True"/>
					</StackPanel>
				</ScrollViewer>
			</DockPanel>
		</Border>

	</Grid>

</UserControl>
