using System;
using System.Collections.Generic;
using System.Linq;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;
using AvaloniaApplication2.Models;
using AvaloniaApplication2.ImageViewer.Drawing;
using AvaloniaApplication2.ImageViewer.Controls;

namespace AvaloniaApplication2.Services
{
    /// <summary>
    /// 新的图像绘制服务，直接使用 RenderView 的形状系统
    /// 解决了旧系统中 RoiShape 和 IShape 不同步的问题
    /// </summary>
    public class ImageDrawingServiceNew
    {
        // 直接引用 RenderView 来管理形状
        private RenderView? _renderView;
        
        // Simple undo stack for basic actions
        private readonly Stack<Action> _undoStack = new();
        
        public ToolMode Mode { get; private set; } = ToolMode.Pan;

        private IBrush _stroke = Brushes.Lime;
        public IBrush Stroke
        {
            get => _stroke;
            set
            {
                if (_stroke == value) return;
                _stroke = value ?? Brushes.Lime;
                
                // 同步到 RenderView
                if (_renderView != null)
                {
                    _renderView.Stroke = _stroke;
                }
                
                InvalidateRequested?.Invoke();
            }
        }

        private double _strokeThickness = 1.0;
        public double StrokeThickness
        {
            get => _strokeThickness;
            set
            {
                if (Math.Abs(_strokeThickness - value) < 0.01) return;
                _strokeThickness = Math.Max(0.1, value);
                
                // 同步到 RenderView
                if (_renderView != null)
                {
                    _renderView.StrokeThickness = _strokeThickness;
                }
                
                InvalidateRequested?.Invoke();
            }
        }

        // Per-image shape storage - 每张图片对应一套形状
        private readonly Dictionary<string, List<IShape>> _shapeStore = new();
        private string _currentImageKey = string.Empty;

        // External hooks
        public Func<Vector, bool>? RequestPanBy; // returns true if handled
        public Action? InvalidateRequested; // notify view to refresh visuals if needed
        public Action<string>? GalleryRefreshRequested; // 通知图库刷新当前图片缩略图

        /// <summary>
        /// 设置关联的 RenderView
        /// </summary>
        public void SetRenderView(RenderView? renderView)
        {
            _renderView = renderView;
            
            if (_renderView != null)
            {
                // 同步当前样式到 RenderView
                _renderView.Stroke = _stroke;
                _renderView.StrokeThickness = _strokeThickness;
            }
        }

        /// <summary>
        /// 获取当前图像的所有形状
        /// </summary>
        public IReadOnlyList<IShape> GetShapes()
        {
            return _renderView?.Shapes ?? new List<IShape>();
        }

        public void SetMode(ToolMode mode)
        {
            Mode = mode;
            
            // 同步到 RenderView
            if (_renderView != null)
            {
                _renderView.Tool = mode switch
                {
                    ToolMode.Rectangle => ImageViewer.Drawing.ToolMode.DrawRect,
                    ToolMode.Circle => ImageViewer.Drawing.ToolMode.DrawCircle,
                    ToolMode.Line => ImageViewer.Drawing.ToolMode.DrawLine,
                    ToolMode.Polygon => ImageViewer.Drawing.ToolMode.DrawPoly,
                    ToolMode.Pencil => ImageViewer.Drawing.ToolMode.DrawFreehand,
                    ToolMode.Eraser => ImageViewer.Drawing.ToolMode.Eraser,
                    _ => ImageViewer.Drawing.ToolMode.Pan
                };
            }
        }

        /// <summary>
        /// 切换图像时保存/恢复形状
        /// </summary>
        public void SwitchImage(string key)
        {
            // 保存当前图像的形状
            if (!string.IsNullOrWhiteSpace(_currentImageKey) && _renderView != null)
            {
                _shapeStore[_currentImageKey] = _renderView.Shapes.ToList();
            }

            // 切换到新图像并恢复该图像的形状
            _currentImageKey = key ?? string.Empty;
            
            if (_renderView != null)
            {
                _renderView.Shapes.Clear();
                
                if (!string.IsNullOrWhiteSpace(_currentImageKey) && 
                    _shapeStore.TryGetValue(_currentImageKey, out var shapes))
                {
                    foreach (var shape in shapes)
                    {
                        _renderView.Shapes.Add(shape);
                    }
                }
                
                _renderView.InvalidateVisual();
            }
            
            InvalidateRequested?.Invoke();
            
            if (!string.IsNullOrWhiteSpace(_currentImageKey))
            {
                GalleryRefreshRequested?.Invoke(_currentImageKey);
            }
        }

        public void SetImageBounds(Rect bounds)
        {
            // 设置图像边界供形状约束使用
            if (_renderView != null)
            {
                ShapeBase.SetImageBounds(bounds);
            }
        }

        /// <summary>
        /// 清除当前图像的所有形状
        /// </summary>
        public void ClearShapes()
        {
            if (_renderView == null || _renderView.Shapes.Count == 0) return;
            
            // Save current shapes for undo
            var shapesToRemove = _renderView.Shapes.ToList();
            _undoStack.Push(() =>
            {
                foreach (var shape in shapesToRemove)
                {
                    _renderView.Shapes.Add(shape);
                }
                _renderView.InvalidateVisual();
                InvalidateRequested?.Invoke();
            });
            
            // Clear all shapes
            _renderView.Shapes.Clear();
            
            // Also clear stored shapes for current image to avoid reappearing on image switch
            if (!string.IsNullOrWhiteSpace(_currentImageKey))
            {
                _shapeStore[_currentImageKey] = new List<IShape>();
            }
            
            _renderView.InvalidateVisual();
            InvalidateRequested?.Invoke();
        }

        public void Undo()
        {
            if (_undoStack.Count > 0)
            {
                var action = _undoStack.Pop();
                action?.Invoke();
            }
        }

        /// <summary>
        /// 获取当前选中的形状信息
        /// </summary>
        public string GetSelectedShapeInfo()
        {
            if (_renderView == null) return string.Empty;
            
            var selectedShape = _renderView.Shapes.FirstOrDefault(s => s.Selected);
            if (selectedShape == null) return string.Empty;
            
            return selectedShape switch
            {
                RectShape rect => $"Rectangle: {rect.Rect.Width:F1} × {rect.Rect.Height:F1}",
                CircleShape circle => $"Circle: R={circle.Radius:F1}",
                LineShape line => $"Line: {line.Length:F1}",
                PolygonShape poly => $"Polygon: {poly.Points.Count} points",
                FreehandShape freehand => $"Freehand: {freehand.Points.Count} points",
                _ => "Shape selected"
            };
        }

        /// <summary>
        /// 检查是否有选中的形状
        /// </summary>
        public bool HasSelectedShape()
        {
            return _renderView?.Shapes.Any(s => s.Selected) ?? false;
        }

        /// <summary>
        /// 获取形状总数
        /// </summary>
        public int GetShapeCount()
        {
            return _renderView?.Shapes.Count ?? 0;
        }
    }
}
