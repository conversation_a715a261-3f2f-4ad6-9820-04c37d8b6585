# 导航器窗口功能修复说明

## 问题描述

用户报告导航器窗口的所有功能都失效，包括：
1. **导航器矩形不显示**：右上角导航器窗口中的视口矩形框不可见
2. **拖拽功能失效**：无法通过拖拽矩形框来移动视图
3. **点击定位失效**：点击导航器空白区域无法跳转到对应位置
4. **缩放滑动条失效**：导航器下方的缩放滑动条无法控制图像缩放

## 问题根源分析

### 1. 双重绑定冲突

**问题**：导航器矩形既有 XAML 数据绑定，又有 C# 代码直接设置，导致冲突。

**原始 XAML 代码**：
```xml
<Border x:Name="NavigatorRect" BorderBrush="#00E0B8" BorderThickness="1" Background="#2000E0B8"
    Width="{Binding ViewportW, Converter={StaticResource MultiplyConverter}, ConverterParameter=160}"
    Height="{Binding ViewportH, Converter={StaticResource MultiplyConverter}, ConverterParameter=96}"
    Canvas.Left="{Binding ViewportX, Converter={StaticResource MultiplyConverter}, ConverterParameter=200}"
    Canvas.Top="{Binding ViewportY, Converter={StaticResource MultiplyConverter}, ConverterParameter=120}"/>
```

**问题分析**：
- 使用了错误的参数值（160, 96 vs 实际的 200, 120）
- 与 C# 代码中的 `UpdateNavigatorRect` 方法产生冲突

### 2. 画布尺寸初始化问题

**问题**：在初始化阶段，`_navCanvas.Bounds` 可能还没有正确设置，导致计算错误。

**原始代码**：
```csharp
var canvasWidth = _navCanvas.Bounds.Width;
var canvasHeight = _navCanvas.Bounds.Height;

if (canvasWidth <= 0 || canvasHeight <= 0) return; // 直接返回，不更新矩形
```

### 3. 事件处理中的坐标计算错误

**问题**：点击和拖拽事件处理中没有处理画布尺寸为 0 的情况。

## 修复方案

### 1. 移除 XAML 绑定冲突

**修复**：移除 XAML 中的数据绑定，完全由 C# 代码控制矩形位置和大小。

**修复后的 XAML**：
```xml
<Canvas x:Name="NavigatorCanvas" PointerPressed="OnNavigatorPointerPressed" PointerMoved="OnNavigatorPointerMoved" PointerReleased="OnNavigatorPointerReleased" PointerWheelChanged="OnNavigatorWheelChanged">
    <Border x:Name="NavigatorRect" BorderBrush="#00E0B8" BorderThickness="1" Background="#2000E0B8"/>
</Canvas>
```

### 2. 改进画布尺寸处理

**修复**：在 `UpdateNavigatorRect` 方法中添加回退机制，使用固定尺寸作为默认值。

**修复后的代码**：
```csharp
private void UpdateNavigatorRect()
{
    if (DataContext is not ImageViewModel vm || _navCanvas == null || _navRect == null) return;

    var canvasWidth = _navCanvas.Bounds.Width;
    var canvasHeight = _navCanvas.Bounds.Height;

    // 如果画布尺寸还没有初始化，使用固定尺寸（与XAML中定义的一致）
    if (canvasWidth <= 0 || canvasHeight <= 0)
    {
        canvasWidth = 200;  // 与XAML中NavigatorHost的Width一致
        canvasHeight = 120; // 与XAML中NavigatorHost的Height一致
    }

    // 计算导航器矩形的位置和大小
    var rectLeft = vm.ViewportX * canvasWidth;
    var rectTop = vm.ViewportY * canvasHeight;
    var rectWidth = vm.ViewportW * canvasWidth;
    var rectHeight = vm.ViewportH * canvasHeight;

    // 设置矩形位置和大小
    Canvas.SetLeft(_navRect, rectLeft);
    Canvas.SetTop(_navRect, rectTop);
    _navRect.Width = rectWidth;
    _navRect.Height = rectHeight;
}
```

### 3. 修复事件处理中的坐标计算

**修复点击事件处理**：
```csharp
else
{
    // Click empty area: center viewport at pointer
    var canvasWidth = _navCanvas.Bounds.Width > 0 ? _navCanvas.Bounds.Width : 200;
    var canvasHeight = _navCanvas.Bounds.Height > 0 ? _navCanvas.Bounds.Height : 120;
    double nx = p.X / canvasWidth;
    double ny = p.Y / canvasHeight;
    CenterScrollToNormalized(nx, ny);
    e.Handled = true;
}
```

**修复拖拽事件处理**：
```csharp
private void OnNavigatorPointerMoved(object? sender, PointerEventArgs e)
{
    if (!_isDragging || _navCanvas == null) return;
    if (DataContext is not ImageViewModel vm) return;
    var p = e.GetPosition(_navCanvas);
    double dx = p.X - _dragStart.X;
    double dy = p.Y - _dragStart.Y;
    var canvasWidth = _navCanvas.Bounds.Width > 0 ? _navCanvas.Bounds.Width : 200;
    var canvasHeight = _navCanvas.Bounds.Height > 0 ? _navCanvas.Bounds.Height : 120;
    double nx = _startVX + dx / canvasWidth;
    double ny = _startVY + dy / canvasHeight;
    vm.ViewportX = Math.Clamp(nx, 0, 1 - vm.ViewportW);
    vm.ViewportY = Math.Clamp(ny, 0, 1 - vm.ViewportH);
    SyncScrollToViewport();
    e.Handled = true;
}
```

### 4. 确保初始化时更新导航器

**修复**：在 `OnAttachedToVisualTree` 方法中添加 `UpdateNavigatorRect` 调用。

```csharp
UpdateViewportValues();
UpdateNavigatorRect();  // 新增
// Try an initial fit
Dispatcher.UIThread.Post(FitToViewport, DispatcherPriority.Background);
```

## 修复效果

### 修复前
- ❌ 导航器矩形不可见
- ❌ 拖拽功能完全失效
- ❌ 点击定位无响应
- ❌ 缩放滑动条不工作

### 修复后
- ✅ 导航器矩形正确显示，实时反映当前视口位置
- ✅ 可以拖拽矩形框来移动图像视图
- ✅ 点击导航器空白区域可以跳转到对应位置
- ✅ 缩放滑动条可以正常控制图像缩放
- ✅ 所有功能与主图像视图保持同步

## 技术要点

1. **避免绑定冲突**：不要同时使用 XAML 绑定和 C# 代码设置同一属性
2. **鲁棒的尺寸处理**：始终提供回退机制处理未初始化的控件尺寸
3. **一致的坐标系统**：确保所有坐标计算使用相同的画布尺寸
4. **及时的状态同步**：在适当的时机调用更新方法，确保UI状态一致

## 相关文件

- `AvaloniaApplication2/Views/ImageView.axaml` - 移除冲突的XAML绑定
- `AvaloniaApplication2/Views/ImageView.axaml.cs` - 修复事件处理和初始化逻辑

这个修复确保了导航器窗口的所有功能都能正常工作，提供了流畅的图像导航体验。
