using System;
using System.Collections.Generic;
using System.Linq;
using Avalonia;
using Avalonia.Input;
using Avalonia.Media;
using AvaloniaApplication2.Models;
using ImageViewer.Drawing;
using ImageViewer.Controls;

namespace AvaloniaApplication2.Services
{
    /// <summary>
    /// 简化的图像绘制服务，直接使用 RenderView 的形状系统
    /// 移除了所有 ROI 相关代码，统一使用控件进行形状绘制
    /// </summary>
    public class ImageDrawingService
    {
        // 直接引用 RenderView 来管理形状
        private RenderView? _renderView;
        
        // Simple undo stack for basic actions
        private readonly Stack<Action> _undoStack = new();
        
        public ToolMode Mode { get; private set; } = ToolMode.Pan;

        private IBrush _stroke = Brushes.Lime;
        public IBrush Stroke
        {
            get => _stroke;
            set
            {
                if (_stroke == value) return;
                _stroke = value ?? Brushes.Lime;
                
                // 同步到 RenderView
                if (_renderView != null)
                {
                    _renderView.Stroke = _stroke;
                }
                
                InvalidateRequested?.Invoke();
            }
        }

        private double _strokeThickness = 1.0;
        public double StrokeThickness
        {
            get => _strokeThickness;
            set
            {
                if (Math.Abs(_strokeThickness - value) < 1e-6) return;
                _strokeThickness = Math.Max(0.1, value);
                
                // 同步到 RenderView
                if (_renderView != null)
                {
                    _renderView.StrokeThickness = _strokeThickness;
                }
                
                InvalidateRequested?.Invoke();
            }
        }

        // External hooks
        public Func<Vector, bool>? RequestPanBy; // returns true if handled
        public Action? InvalidateRequested; // notify view to refresh visuals if needed
        public Action<string>? GalleryRefreshRequested; // 通知图库刷新当前图片缩略图

        /// <summary>
        /// 设置关联的 RenderView
        /// </summary>
        public void SetRenderView(RenderView? renderView)
        {
            _renderView = renderView;
            
            if (_renderView != null)
            {
                // 同步当前样式到 RenderView
                _renderView.Stroke = _stroke;
                _renderView.StrokeThickness = _strokeThickness;
            }
        }

        /// <summary>
        /// 获取当前图像的所有形状
        /// </summary>
        public IReadOnlyList<IShape> GetShapes()
        {
            return _renderView?.Shapes ?? new List<IShape>();
        }

        public void SetMode(ToolMode mode)
        {
            Mode = mode;
            
            // 同步到 RenderView
            if (_renderView != null)
            {
                _renderView.Tool = mode switch
                {
                    ToolMode.Rectangle => ImageViewer.Drawing.ToolMode.DrawRect,
                    ToolMode.Circle => ImageViewer.Drawing.ToolMode.DrawCircle,
                    ToolMode.Line => ImageViewer.Drawing.ToolMode.DrawLine,
                    ToolMode.Polygon => ImageViewer.Drawing.ToolMode.DrawPolygon,
                    ToolMode.Pencil => ImageViewer.Drawing.ToolMode.DrawFreehand,
                    ToolMode.Eraser => ImageViewer.Drawing.ToolMode.Eraser,
                    _ => ImageViewer.Drawing.ToolMode.Edit
                };
            }
        }

        /// <summary>
        /// 切换图像时保存/恢复形状
        /// </summary>
        public void SwitchImage(string key)
        {
            // RenderView 现在自动处理形状切换
            if (_renderView != null)
            {
                _renderView.ImagePath = key;
            }
            
            InvalidateRequested?.Invoke();
            
            if (!string.IsNullOrWhiteSpace(key))
            {
                GalleryRefreshRequested?.Invoke(key);
            }
        }

        public void SetImageBounds(Rect bounds)
        {
            // RenderView 自动处理图像边界
        }

        /// <summary>
        /// 清除当前图像的所有形状
        /// </summary>
        public void ClearShapes()
        {
            if (_renderView == null || _renderView.Shapes.Count == 0) return;
            
            // Save current shapes for undo
            var shapesToRemove = _renderView.Shapes.ToList();
            _undoStack.Push(() =>
            {
                foreach (var shape in shapesToRemove)
                {
                    _renderView.Shapes.Add(shape);
                }
                _renderView.InvalidateVisual();
                InvalidateRequested?.Invoke();
            });
            
            // Clear all shapes using RenderView's method
            _renderView.ClearCurrentImageShapes();
            
            InvalidateRequested?.Invoke();
        }

        public void Undo()
        {
            if (_undoStack.Count > 0)
            {
                var action = _undoStack.Pop();
                action?.Invoke();
            }
        }

        /// <summary>
        /// 获取当前选中的形状信息
        /// </summary>
        public string GetSelectedShapeInfo()
        {
            if (_renderView == null) return string.Empty;
            
            var selectedShape = _renderView.Shapes.FirstOrDefault(s => s.Selected);
            if (selectedShape == null) return string.Empty;
            
            return selectedShape switch
            {
                RectShape rect => $"Rectangle: {rect.Rect.Width:F1} × {rect.Rect.Height:F1}",
                CircleShape circle => $"Circle: R={circle.Radius:F1}",
                LineShape line => $"Line: {line.Length:F1}",
                PolygonShape poly => $"Polygon: {poly.Points.Count} points",
                _ => "Shape selected"
            };
        }

        /// <summary>
        /// 获取当前形状数量
        /// </summary>
        public int GetShapeCount()
        {
            return _renderView?.Shapes.Count ?? 0;
        }

        /// <summary>
        /// 检查是否有选中的形状
        /// </summary>
        public bool HasSelectedShape()
        {
            return _renderView?.Shapes.Any(s => s.Selected) ?? false;
        }

        /// <summary>
        /// 取消所有形状的选择
        /// </summary>
        public void ClearSelection()
        {
            if (_renderView == null) return;
            
            foreach (var shape in _renderView.Shapes)
            {
                shape.Selected = false;
            }
            
            _renderView.InvalidateVisual();
            InvalidateRequested?.Invoke();
        }

        /// <summary>
        /// 删除选中的形状
        /// </summary>
        public void DeleteSelectedShapes()
        {
            if (_renderView == null) return;
            
            var selectedShapes = _renderView.Shapes.Where(s => s.Selected).ToList();
            if (selectedShapes.Count == 0) return;
            
            // Save for undo
            _undoStack.Push(() =>
            {
                foreach (var shape in selectedShapes)
                {
                    _renderView.Shapes.Add(shape);
                }
                _renderView.InvalidateVisual();
                InvalidateRequested?.Invoke();
            });
            
            // Remove selected shapes
            foreach (var shape in selectedShapes)
            {
                _renderView.Shapes.Remove(shape);
            }
            
            _renderView.InvalidateVisual();
            InvalidateRequested?.Invoke();
        }
    }
}
